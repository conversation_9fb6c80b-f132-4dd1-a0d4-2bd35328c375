import { Text, View } from "react-native"
import TGAmbassadorLogo from '../../../assets/svg/TGAmbassadorLogo.svg'
import { StyleSheet } from "react-native"
import { colors } from "../../../theme/theme"
import { Size, Spacing, Typography } from "../../../utils/responsiveUI"

const TGAmbassadorLable = ({ containerStyle = {}, showBadgeText = true , iconHeight= 20, iconWidth= 20}) => {
    return <>
        <View style={
            [styles.badgeContainer,
            showBadgeText ? {width: Size.SIZE_120 } : { paddingHorizontal: Spacing.SCALE_3},
                containerStyle]}>
            <TGAmbassadorLogo width={iconWidth} height={iconHeight} />
            {showBadgeText && <Text style={styles.badgeText}>TG AMBASSADOR</Text>}
        </View>
    </>
}

export default TGAmbassadorLable


const styles = StyleSheet.create({
    badgeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.white,
        borderRadius: 70,
        paddingVertical: Spacing.SCALE_4,
        paddingHorizontal: Spacing.SCALE_4,
        marginVertical: Spacing.SCALE_4,
        // Shadow for iOS
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.11,
        shadowRadius: 2,
        // Shadow for Android
        elevation: 4,
    },
    badgeText: {
        fontSize: Typography.FONT_SIZE_10,
        lineHeight: Size.SIZE_11,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        paddingLeft: Spacing.SCALE_4
    }
})