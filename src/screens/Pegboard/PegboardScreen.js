import React, { useContext, useState, useEffect } from 'react';
import {
    View,
    Text,
    TouchableHighlight,
    FlatList,
    RefreshControl,
    TouchableOpacity,
    Platform,
    StatusBar,
} from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';

import styles from './style';
import { colors } from '../../theme/theme';
import Golf from '../../assets/svg/cil_golf.svg';
import ArrowRightIcon from '../../assets/images/arrow-right-black.svg';
import { Size, Spacing } from '../../utils/responsiveUI';
import { GET_PEGBOARD_DETAILS_V3, GET_PEGBOARD_V2 } from '../../service/EndPoint';
import { AuthContext } from '../../context/AuthContext';
import { fetcher } from '../../service/fetcher';
import routes from '../../config/routes';
import config from '../../config';
import { GlobalContext } from '../../context/contextApi';
import { ADMIN_USER_ID, SUCCESS, VIEW_HIDDEN_PEGBOARD } from '../../utils/constants/strings';
import showToast from '../../components/toast/CustomToast';
import ProfileHeader from '../../components/layout/ProfileHeader';
import TripleDotToolTip from '../../components/tooltip/TripleDotToolTip';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import PegboardAction from './PegboardAction';
import { apiServices } from '../../service/apiServices';

export default ({ route }) => {
    const { user, refreshUser, isVisible } = useContext(AuthContext);
    const [refreshing, setRefresh] = useState(false);
    const [showTooltip, setShowTooltip] = useState(false);
    const [state, setState] = useState({
        pegboardsList: [],
        isLoading: true,
    });
    const navigation = useNavigation();
    const isFocused = useIsFocused();
    const { actions, state: globalState } = useContext(GlobalContext);
    const animatedOpacity = useSharedValue(0);
    const animatedStyle = useAnimatedStyle(() => ({
        opacity: animatedOpacity.value,
    }));
    const [showHiddenPegboard, setShowHiddenPegboard] = useState(route?.params?.showHiddenPegboard || false);
    const [showPegboardAction, setShowPegboardAction] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);

    useEffect(() => {
        if(isFocused) {
            getPegboardList();
        }
    }, [showHiddenPegboard, isFocused]);

    useEffect(() => {
        if (showTooltip) {
            animatedOpacity.value = withTiming(1, { duration: 300 });
        } else {
            animatedOpacity.value = withTiming(0, { duration: 300 });
        }
    }, [showTooltip]);

    const getPegboardList = () => {
        actions.setAppLoader(true);
        fetcher({
            method: 'POST',
            endpoint: GET_PEGBOARD_V2,
            body: {
                userId: user?.id,
                hidden: route?.params?.showHiddenPegboard || false,
            },
        })
            .then((res) => {
                setState({
                    ...state,
                    pegboardsList: res?.data,
                    isLoading: false,
                });
                setTimeout(() => {
                    actions.setAppLoader(false);
                }, 1000);
            })
            .catch((err) => {
                setState({
                    ...state,
                    isLoading: false,
                });
            });
    };

    const _onRefresh = () => {
        getPegboardList();
    };

    const handleGetClubList = async (item) => {
        try {
            fetcher({
                method: 'POST',
                endpoint: GET_PEGBOARD_DETAILS_V3,
                body: {
                    userId: user?.id,
                    pegboardId: item?.id,
                },
            })
                .then((res) => {
                    if (res.status) {
                        navigation.navigate(config.routes.PEGBOARD_ADD_CLUB, {
                            pegboardId: item?.id,
                            pegBoardName: item?.name,
                            totalClubs: item?.totalClubs,
                            pegboardId: item?.id,
                            creatorId: item.creatorId,
                            isPrivate: item.isPrivate,
                            item: item,
                            callback: getPegboardList,
                        });
                    }
                })
                .catch((err) => {
                    showToast({});
                });
        } catch (error) {
            console.log(error);
        }
    };

    const handleHidePegboard = (item) => {
        actions.setAppLoader(true);
        apiServices
            .togglePegboardVisibility({
                pegboardId: item?.id,
                isHidden: !item?.isHidden,
                userId: user?.id,
            })
            .then((res) => {
                getPegboardList();
                showToast({
                    header: SUCCESS,
                    message: route?.params?.showHiddenPegboard
                        ? 'Pegboard restored to your list'
                        : 'Pegboard hidden from view',
                    type: SUCCESS,
                });
            });
    };

    const renderItem = ({ item }) => {
        return (
            <TouchableHighlight
                style={styles.listStyle}
                onPress={() => {
                    setShowPegboardAction(true);
                    setSelectedItem(item);
                }}
                underlayColor={colors.lightgray}>
                <>
                    <View style={{ flex: 0.1 }}>
                        <Golf height={Size.SIZE_20} width={Size.SIZE_20} />
                    </View>
                    <View style={{ flex: 0.8 }}>
                        <Text style={[styles.font16, styles.spacing6]}>{item?.name}</Text>

                        <View style={{ flexDirection: 'row', paddingVertical: 8 }}>
                            <Text style={[styles.font12, { color: colors.darkgray }]}>Created by -</Text>
                            {item.creatorId !== ADMIN_USER_ID && item.creatorId !== user?.id ? (
                                <TouchableOpacity
                                    onPress={() => {
                                        navigation.navigate('UserProfileScreen', {
                                            selectedUser: { id: item.creatorId },
                                        });
                                    }}>
                                    <Text
                                        style={{
                                            color: colors.tealRgb,
                                            textAlign: 'center',
                                            alignSelf: 'center',
                                            lineHeight: Platform.OS != 'ios' ? 15 : 0,
                                        }}>
                                        {' '}
                                        {item?.creatorName}
                                    </Text>
                                </TouchableOpacity>
                            ) : (
                                <Text style={{ color: colors.lightBlack, lineHeight: Platform.OS != 'ios' ? 15 : 0 }}>
                                    {' '}
                                    {item?.creatorName}
                                </Text>
                            )}
                        </View>

                        <View style={{ flexDirection: 'row' }}>
                            <Text style={[styles.font12, { color: colors.darkgray }]}>
                                Total : <Text style={{ color: colors.lightBlack }}>{item?.totalClubs}</Text>
                            </Text>
                            <Text
                                style={[
                                    styles.font12,
                                    {
                                        color: colors.darkgray,
                                        marginHorizontal: Spacing.SCALE_6,
                                    },
                                ]}>
                                |
                            </Text>
                            <Text style={[styles.font12]}>
                                Played : <Text style={{ color: colors.lightBlack }}>{item?.totalPlayedClubs} </Text>
                            </Text>
                        </View>
                    </View>
                    <View
                        style={{
                            flex: 0.1,
                            alignItems: 'flex-end',
                            justifyContent: 'center',
                        }}>
                        <ArrowRightIcon height={Size.SIZE_16} width={Size.SIZE_9} />
                    </View>
                </>
            </TouchableHighlight>
        );
    };

    return (
        <>
            <StatusBar backgroundColor={colors.whiteRGB} barStyle="dark-content" />
            <View
                style={{
                    width: '100%',
                    zIndex: 100,
                    backgroundColor: colors.whiteRGB,
                    flex: 1,
                }}>
                <View style={{ position: 'relative' }}>
                    <ProfileHeader
                        title={route?.params?.showHiddenPegboard ? 'Hidden Pegboard' : 'Pegboard'}
                        headerTitleStyle={styles.headerTitleStyle}
                        backButtonFillColor={colors.lightBlack}
                        containerStyle={{
                            backgroundColor: colors.whiteRGB,
                            paddingBottom: Spacing.SCALE_10,
                        }}
                        iconShow={'Pegboard'}
                        onClick={() => {
                            actions.setPegboardDetails({});
                            navigation.navigate(routes.CREATE_PEGBOARD_SCREEN);
                        }}
                        showTripleDot={route?.params?.showHiddenPegboard ? false : true}
                        onPressTripleDot={() => {
                            setShowTooltip(!showTooltip);
                        }}
                    />
                    {showTooltip && (
                        <Animated.View style={[styles.tooltipStyle, animatedStyle]}>
                            <TripleDotToolTip
                                tooltipText={VIEW_HIDDEN_PEGBOARD}
                                onPress={() => {
                                    setShowTooltip(false);
                                    navigation.push(config.routes.PEGBOARD, {
                                        showHiddenPegboard: !showHiddenPegboard,
                                    });
                                }}
                            />
                        </Animated.View>
                    )}
                </View>
                <View style={styles.bodyContainer}>
                    <FlatList
                        keyExtractor={(item, index) => index.toString()}
                        data={state.pegboardsList}
                        renderItem={renderItem}
                        refreshControl={<RefreshControl onRefresh={_onRefresh} refreshing={refreshing} />}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{
                            alignContent: 'center',
                            paddingTop: Spacing.SCALE_12,
                        }}
                        ListEmptyComponent={() => {
                            return (
                                <View style={styles.emptyContainer}>
                                    {route?.params?.showHiddenPegboard && !globalState.appLoader ? (
                                        <Text style={styles.emptyText}>No Pegboards Hidden!</Text>
                                    ) : null}
                                </View>
                            );
                        }}
                    />
                </View>
            </View>
            <PegboardAction
                popupState={[showPegboardAction, setShowPegboardAction]}
                onPressViewPegboard={() => {
                    setShowPegboardAction(false);
                    actions.setPegboardDetails({
                        id: selectedItem?.id,
                        name: selectedItem?.name,
                        isPrivate: selectedItem?.isPrivate,
                        myTGGroups: selectedItem?.my_tg_group_id,
                        forFrieds: selectedItem?.forFriends,
                        chatChannels: selectedItem?.chatChannels,
                    });
                    handleGetClubList(selectedItem);
                }}
                onPressHidePegboard={() => {
                    setShowPegboardAction(false);
                    if (route?.params?.showHiddenPegboard) {
                        handleHidePegboard(selectedItem);
                    } else {
                        setTimeout(() => {
                            navigation.navigate('DeleteChannelConfirmationPopup', {
                                popupHeader: route?.params?.showHiddenPegboard ? 'Unhide Pegboard' : 'Hide Pegboard',
                                popupSubText: `Are you sure you want to ${
                                    route?.params?.showHiddenPegboard ? 'unhide' : 'hide'
                                } this pegboard from your profile?`,
                                firstBtnLabel: 'Dismiss',
                                secondBtnLabel: route?.params?.showHiddenPegboard ? 'Unhide' : 'Hide',
                                handleYesButton: () => {
                                    handleHidePegboard(selectedItem);
                                },
                                handleFirstBtnPress: () => {
                                    setShowPegboardAction(false);
                                },
                            });
                        }, 700);
                    }
                }}
                selectedItem={selectedItem}
                userId={user?.id}
            />
        </>
    );
};
