import { Modal, Pressable, Text, TouchableWithoutFeedback, View } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import moment from 'moment';

import MuteICon from '../../../assets/svg/volume-mute.svg';
import Check from '../../../assets/svg/Check.svg';
import styles from '../style/style';
import { colors } from '../../../theme/theme';
import { CancelButton } from '../../../components/buttons';
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import useClient from '../../../hooks/useClient';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import { GlobalContext } from '../../../context/contextApi';
import { DISMISS_TEXT, MUTE_ACCOUNT_POPUP_TEXT } from '../../../utils/constants/strings';

const UnMuteAccountPopup = ({ user, popupState }) => {
    const { state, actions } = useContext(GlobalContext);
    const [showUnMutePopup, setShowUnMutePopup] = popupState;
    const [check, setCheck] = useState(false);
    const client = useClient();

    const handleUnMuteBtn = async () => {
        actions.setAppLoader(true);
        try {
            await client.request(UPDATE_USER, {
                user_id: user?.id,
                user: {
                    muted: false,
                },
            });
        } catch (error) {
        }
        actions.setHomeScreenPopupState(2);
    };

    const handleDismissBtn = async () => {
        actions.setAppLoader(true);
        try {
            await client.request(UPDATE_USER, {
                user_id: user?.id,
                user: {
                    last_muted_prompt: check
                        ? moment().add(30, 'days').format()
                        : moment().add(24, 'hours').format('YYYY-MM-DD HH:mm:ss'),
                },
            });
        } catch (error) {
        }
        actions.setHomeScreenPopupState(2);
    };

    if (showUnMutePopup)
        return (
            <View style={styles.popupWrapper}>
                <View style={styles.iconWrapper}>
                    <MuteICon width={44} height={44} />
                </View>
                <Text style={styles.popupHeader}>Muted Account</Text>
                <Text style={styles.popupSubHeader}>{MUTE_ACCOUNT_POPUP_TEXT}</Text>
                <Pressable style={styles.checkBox} onPress={() => setCheck((prev) => !prev)}>
                    {check ? (
                        <Pressable style={styles.checkBoxContainer}>
                            <Check />
                        </Pressable>
                    ) : (
                        <Pressable style={styles.checkBoxContainer1} />
                    )}
                    <Text style={styles.text}>{DISMISS_TEXT}</Text>
                </Pressable>
                <View style={styles.btnWrapper}>
                    <CancelButton
                        customStyle={[styles.customStyle, { backgroundColor: colors.lightgray }]}
                        textStyle={styles.btn1TextStyle}
                        text="Dismiss"
                        onPress={handleDismissBtn}
                    />
                    <TealButtonNew
                        btnStyle={styles.customStyle}
                        textStyle={styles.btn2TextStyle}
                        text="Unmute"
                        onPress={handleUnMuteBtn}
                        loading={false}
                    />
                </View>
            </View>
        );
    else return null;
};

export default UnMuteAccountPopup;
