
import { CREATE_NOTIFICATION } from '../../graphql/mutations/notification';
import { fetcher } from '../../service/fetcher';

async function createNotification({ client, user_id, type, message, text_message }) {
    const notification = await client.request(
        CREATE_NOTIFICATION,
        {
            notifications: [
                {
                    user_id,
                    type,
                    message,
                    text_message
                },
            ],
        },
    ).catch(console.log);
    return notification
}

async function sendNotification(url, params) {
    const response = await fetcher({
        endpoint: url,
        method: 'POST',
        body: params,
    }).catch(console.log);
    return response;
}
export { createNotification, sendNotification }